// src/Components/FluentSplitter.cpp
#include "FluentQt/Components/FluentSplitter.h"
#include "FluentQt/Styling/FluentTheme.h"
#include <QPainter>
#include <QMouseEvent>
#include <QPropertyAnimation>
#include <QEasingCurve>
#include <QTimer>

namespace FluentQt::Components {

FluentSplitter::FluentSplitter(Qt::Orientation orientation, QWidget* parent)
    : QSplitter(orientation, parent)
    , m_animatedResize(true)
    , m_snapToPosition(false)
    , m_snapThreshold(20)
    , m_handleWidth(8)
    , m_hoverAnimation(new QPropertyAnimation(this))
    , m_resizeTimer(new QTimer(this))
{
    setupUI();
    setupAnimation();
    
    // Connect to theme changes
    connect(&Styling::FluentTheme::instance(), &Styling::FluentTheme::themeChanged,
            this, &FluentSplitter::updateTheme);
}

void FluentSplitter::setupUI() {
    setHandleWidth(m_handleWidth);
    setChildrenCollapsible(true);
    
    // Apply Fluent styling
    updateTheme();
    
    // Setup resize timer for smooth animations
    m_resizeTimer->setSingleShot(true);
    m_resizeTimer->setInterval(100);
    connect(m_resizeTimer, &QTimer::timeout, this, &FluentSplitter::onResizeFinished);
}

void FluentSplitter::setupAnimation() {
    m_hoverAnimation->setDuration(150);
    m_hoverAnimation->setEasingCurve(QEasingCurve::OutCubic);
}

void FluentSplitter::setAnimatedResize(bool animated) {
    if (m_animatedResize == animated) return;
    
    m_animatedResize = animated;
}

void FluentSplitter::setSnapToPosition(bool snap) {
    if (m_snapToPosition == snap) return;
    
    m_snapToPosition = snap;
}

void FluentSplitter::setSnapThreshold(int threshold) {
    if (m_snapThreshold == threshold) return;
    
    m_snapThreshold = threshold;
}

void FluentSplitter::setHandleWidth(int width) {
    if (m_handleWidth == width) return;
    
    m_handleWidth = width;
    QSplitter::setHandleWidth(width);
}

void FluentSplitter::addWidget(QWidget* widget) {
    QSplitter::addWidget(widget);
    updateHandles();
}

void FluentSplitter::insertWidget(int index, QWidget* widget) {
    QSplitter::insertWidget(index, widget);
    updateHandles();
}

QWidget* FluentSplitter::replaceWidget(int index, QWidget* widget) {
    QWidget* old = QSplitter::replaceWidget(index, widget);
    updateHandles();
    return old;
}

void FluentSplitter::animateToSizes(const QList<int>& sizes) {
    if (!m_animatedResize || sizes.size() != count()) {
        setSizes(sizes);
        return;
    }
    
    // Create animation for smooth size transition
    QList<int> currentSizes = this->sizes();
    
    // For now, just set sizes directly
    // TODO: Implement smooth animation between sizes
    setSizes(sizes);
}

void FluentSplitter::collapseWidget(int index) {
    if (index < 0 || index >= count()) return;
    
    QList<int> sizes = this->sizes();
    if (index < sizes.size()) {
        sizes[index] = 0;
        animateToSizes(sizes);
    }
}

void FluentSplitter::expandWidget(int index, int size) {
    if (index < 0 || index >= count()) return;
    
    QList<int> sizes = this->sizes();
    if (index < sizes.size()) {
        if (size <= 0) {
            // Calculate reasonable default size
            size = (orientation() == Qt::Horizontal ? width() : height()) / count();
        }
        sizes[index] = size;
        animateToSizes(sizes);
    }
}

bool FluentSplitter::isWidgetCollapsed(int index) const {
    if (index < 0 || index >= count()) return false;
    
    QList<int> sizes = this->sizes();
    return index < sizes.size() && sizes[index] == 0;
}

QSplitterHandle* FluentSplitter::createHandle() {
    return new FluentSplitterHandle(orientation(), this);
}

void FluentSplitter::resizeEvent(QResizeEvent* event) {
    QSplitter::resizeEvent(event);
    
    if (m_snapToPosition) {
        m_resizeTimer->start();
    }
}

void FluentSplitter::updateTheme() {
    const auto& theme = Styling::FluentTheme::instance();
    const auto& palette = theme.currentPalette();
    
    // Update splitter styling
    QString styleSheet = QString(R"(
        QSplitter::handle {
            background-color: %1;
        }
        QSplitter::handle:hover {
            background-color: %2;
        }
        QSplitter::handle:pressed {
            background-color: %3;
        }
    )").arg(palette.border.name())
       .arg(palette.accent.lighter(150).name())
       .arg(palette.accent.name());
    
    setStyleSheet(styleSheet);
}

void FluentSplitter::updateHandles() {
    // Update all handles with current styling
    for (int i = 0; i < count() - 1; ++i) {
        if (auto* handle = this->handle(i)) {
            // Apply any handle-specific updates
        }
    }
}

void FluentSplitter::onResizeFinished() {
    if (!m_snapToPosition) return;
    
    // Implement snap-to-position logic
    QList<int> sizes = this->sizes();
    bool needsAdjustment = false;
    
    for (int i = 0; i < sizes.size(); ++i) {
        if (sizes[i] > 0 && sizes[i] < m_snapThreshold) {
            sizes[i] = 0; // Snap to collapsed
            needsAdjustment = true;
        }
    }
    
    if (needsAdjustment) {
        setSizes(sizes);
    }
}

// FluentSplitterHandle implementation
FluentSplitterHandle::FluentSplitterHandle(Qt::Orientation orientation, QSplitter* parent)
    : QSplitterHandle(orientation, parent)
    , m_hovered(false)
    , m_pressed(false)
{
    setAttribute(Qt::WA_Hover, true);
}

void FluentSplitterHandle::enterEvent(QEnterEvent* event) {
    QSplitterHandle::enterEvent(event);
    m_hovered = true;
    update();
}

void FluentSplitterHandle::leaveEvent(QEvent* event) {
    QSplitterHandle::leaveEvent(event);
    m_hovered = false;
    update();
}

void FluentSplitterHandle::mousePressEvent(QMouseEvent* event) {
    QSplitterHandle::mousePressEvent(event);
    if (event->button() == Qt::LeftButton) {
        m_pressed = true;
        update();
    }
}

void FluentSplitterHandle::mouseReleaseEvent(QMouseEvent* event) {
    QSplitterHandle::mouseReleaseEvent(event);
    if (event->button() == Qt::LeftButton) {
        m_pressed = false;
        update();
    }
}

void FluentSplitterHandle::paintEvent(QPaintEvent* event) {
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    const auto& theme = Styling::FluentTheme::instance();
    const auto& palette = theme.currentPalette();
    
    QColor handleColor = palette.border;
    if (m_pressed) {
        handleColor = palette.accent;
    } else if (m_hovered) {
        handleColor = palette.accent.lighter(150);
    }
    
    // Draw handle
    QRect handleRect = rect();
    if (orientation() == Qt::Horizontal) {
        // Vertical handle for horizontal splitter
        int centerX = handleRect.center().x();
        handleRect = QRect(centerX - 1, handleRect.top() + 4, 2, handleRect.height() - 8);
    } else {
        // Horizontal handle for vertical splitter
        int centerY = handleRect.center().y();
        handleRect = QRect(handleRect.left() + 4, centerY - 1, handleRect.width() - 8, 2);
    }
    
    painter.fillRect(handleRect, handleColor);
    
    // Draw grip dots for visual feedback
    if (m_hovered || m_pressed) {
        painter.setBrush(handleColor.darker(120));
        painter.setPen(Qt::NoPen);
        
        QPoint center = rect().center();
        int dotSize = 2;
        int spacing = 6;
        
        if (orientation() == Qt::Horizontal) {
            // Vertical dots
            for (int i = -1; i <= 1; ++i) {
                QPoint dotPos(center.x(), center.y() + i * spacing);
                painter.drawEllipse(dotPos, dotSize, dotSize);
            }
        } else {
            // Horizontal dots
            for (int i = -1; i <= 1; ++i) {
                QPoint dotPos(center.x() + i * spacing, center.y());
                painter.drawEllipse(dotPos, dotSize, dotSize);
            }
        }
    }
}

QSize FluentSplitterHandle::sizeHint() const {
    QSize size = QSplitterHandle::sizeHint();
    if (orientation() == Qt::Horizontal) {
        size.setWidth(8);
    } else {
        size.setHeight(8);
    }
    return size;
}

} // namespace FluentQt::Components

// examples/PerformanceOptimizationExample.cpp
#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QTabWidget>
#include <QGroupBox>
#include <QLabel>
#include <QProgressBar>
#include <QSlider>
#include <QSpinBox>
#include <QCheckBox>
#include <QPushButton>
#include <QListWidget>
#include <QTableWidget>
#include <QTextEdit>
#include <QTimer>
#include <QElapsedTimer>
#include <QThread>
#include <QThreadPool>
#include <QRunnable>
#include <QMutex>
#include <QRandomGenerator>
#include <QStandardItemModel>
#include <QScrollArea>
#include <QFileDialog>
#include <QMessageBox>
#include <QClipboard>
#include <QDateTime>
#include <QProcess>
#include <QCoreApplication>

#include "FluentQt/Styling/FluentTheme.h"
#include "FluentQt/Components/FluentButton.h"
#include "FluentQt/Components/FluentCard.h"
#include "FluentQt/Components/FluentTreeView.h"
#include "FluentQt/Components/FluentComboBox.h"
#include "FluentQt/Components/FluentTextInput.h"
#include "FluentQt/Components/FluentChartView.h"
#include "FluentQt/Animation/FluentAnimator.h"
#include "FluentQt/Core/FluentPerformance.h"

using namespace FluentQt;

class PerformanceOptimizationExample : public QMainWindow {
    Q_OBJECT

public:
    explicit PerformanceOptimizationExample(QWidget* parent = nullptr);

private slots:
    // Performance monitoring
    void onFrameRateChanged(double fps);
    void onPerformanceWarning(const QString& message);
    void updatePerformanceMetrics();
    void togglePerformanceMonitoring(bool enabled);
    
    // Memory management
    void runMemoryStressTest();
    void runGarbageCollection();
    void analyzeMemoryUsage();
    void toggleMemoryOptimization(bool enabled);
    
    // Large dataset handling
    void generateLargeDataset();
    void enableVirtualScrolling(bool enabled);
    void toggleLazyLoading(bool enabled);
    void benchmarkDataOperations();
    
    // Animation optimization
    void toggleAnimationOptimization(bool enabled);
    void adjustAnimationQuality(int quality);
    void runAnimationBenchmark();
    
    // Rendering optimization
    void toggleHardwareAcceleration(bool enabled);
    void adjustRenderingQuality(int quality);
    void runRenderingBenchmark();
    
    // Threading and concurrency
    void runMultithreadedOperation();
    void adjustThreadPoolSize(int size);
    void runConcurrencyBenchmark();
    
    // Profiling and debugging
    void startProfiling();
    void stopProfiling();
    void exportProfilingData();
    void clearProfilingData();

private:
    void setupUI();
    void setupPerformanceMonitoring();
    void setupMemoryManagement();
    void setupDatasetHandling();
    void setupAnimationOptimization();
    void setupRenderingOptimization();
    void setupThreadingControls();
    void setupProfilingTools();
    void setupConnections();
    void setupTheme();
    
    // Helper methods
    void createPerformanceMetricsPanel();
    void createMemoryManagementPanel();
    void createDatasetPanel();
    void createOptimizationPanel();
    void createProfilingPanel();
    void populateLargeDataset(int itemCount);
    void simulateHeavyComputation();
    void profileOperation(const QString& operation, std::function<void()> func);
    void logPerformanceEvent(const QString& event);
    void updateMemoryDisplay();
    void updateFPSDisplay();
    void optimizeForLowEndDevice();
    void optimizeForHighEndDevice();

private:
    // Main UI components
    QWidget* m_centralWidget{nullptr};
    QTabWidget* m_mainTabs{nullptr};
    QSplitter* m_mainSplitter{nullptr};
    
    // Performance monitoring
    QGroupBox* m_performanceGroup{nullptr};
    QLabel* m_fpsLabel{nullptr};
    QLabel* m_memoryLabel{nullptr};
    QLabel* m_cpuLabel{nullptr};
    QProgressBar* m_performanceBar{nullptr};
    QCheckBox* m_monitoringEnabled{nullptr};
    Components::FluentChartView* m_performanceChart{nullptr};
    
    // Memory management
    QGroupBox* m_memoryGroup{nullptr};
    QLabel* m_heapSizeLabel{nullptr};
    QLabel* m_allocatedLabel{nullptr};
    QLabel* m_availableLabel{nullptr};
    QProgressBar* m_memoryUsageBar{nullptr};
    Components::FluentButton* m_gcButton{nullptr};
    Components::FluentButton* m_memoryTestButton{nullptr};
    QCheckBox* m_memoryOptimization{nullptr};
    
    // Large dataset handling
    QGroupBox* m_datasetGroup{nullptr};
    Components::FluentTreeView* m_largeTreeView{nullptr};
    QTableWidget* m_largeTableWidget{nullptr};
    QListWidget* m_largeListWidget{nullptr};
    QSpinBox* m_datasetSizeSpinBox{nullptr};
    QCheckBox* m_virtualScrolling{nullptr};
    QCheckBox* m_lazyLoading{nullptr};
    Components::FluentButton* m_generateDataButton{nullptr};
    Components::FluentButton* m_benchmarkButton{nullptr};
    
    // Animation optimization
    QGroupBox* m_animationGroup{nullptr};
    QSlider* m_animationQualitySlider{nullptr};
    QCheckBox* m_animationOptimization{nullptr};
    QCheckBox* m_hardwareAcceleration{nullptr};
    Components::FluentButton* m_animationBenchmarkButton{nullptr};
    QLabel* m_animationQualityLabel{nullptr};
    
    // Rendering optimization
    QGroupBox* m_renderingGroup{nullptr};
    QSlider* m_renderingQualitySlider{nullptr};
    QCheckBox* m_antialiasing{nullptr};
    QCheckBox* m_textureOptimization{nullptr};
    Components::FluentButton* m_renderingBenchmarkButton{nullptr};
    QLabel* m_renderingQualityLabel{nullptr};
    
    // Threading and concurrency
    QGroupBox* m_threadingGroup{nullptr};
    QSpinBox* m_threadPoolSizeSpinBox{nullptr};
    QLabel* m_activeThreadsLabel{nullptr};
    Components::FluentButton* m_multithreadTestButton{nullptr};
    Components::FluentButton* m_concurrencyBenchmarkButton{nullptr};
    QProgressBar* m_threadingProgressBar{nullptr};
    
    // Profiling and debugging
    QGroupBox* m_profilingGroup{nullptr};
    Components::FluentButton* m_startProfilingButton{nullptr};
    Components::FluentButton* m_stopProfilingButton{nullptr};
    Components::FluentButton* m_exportProfilingButton{nullptr};
    Components::FluentButton* m_clearProfilingButton{nullptr};
    QTextEdit* m_profilingLog{nullptr};
    QListWidget* m_performanceEvents{nullptr};
    
    // Performance monitoring system
    Core::FluentPerformanceMonitor* m_performanceMonitor{nullptr};
    QTimer* m_metricsUpdateTimer{nullptr};
    QElapsedTimer m_operationTimer;
    
    // Data management
    QStandardItemModel* m_largeDataModel{nullptr};
    QList<QVariantMap> m_largeDataset;
    
    // State variables
    bool m_performanceMonitoringEnabled{true};
    bool m_memoryOptimizationEnabled{false};
    bool m_virtualScrollingEnabled{false};
    bool m_lazyLoadingEnabled{false};
    bool m_animationOptimizationEnabled{false};
    bool m_hardwareAccelerationEnabled{true};
    bool m_profilingActive{false};
    int m_animationQuality{100};
    int m_renderingQuality{100};
    int m_threadPoolSize{4};
    
    // Performance metrics
    double m_currentFPS{60.0};
    size_t m_memoryUsage{0};
    double m_cpuUsage{0.0};
    QStringList m_performanceLog;
    QMap<QString, qint64> m_profilingResults;
    
    // Threading
    QThreadPool* m_threadPool{nullptr};
    QMutex m_dataMutex;
    
    // Clipboard for data export
    QClipboard* m_clipboard{nullptr};
};
